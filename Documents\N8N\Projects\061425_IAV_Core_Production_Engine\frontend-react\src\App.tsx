import React from 'react';
import './index.css';

function App() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            🚀 IAV Mission Control
          </h1>
          <p className="text-xl text-slate-300 mb-8">
            Command Center for Viral Content Operations
          </p>
          <div className="glass-panel p-8 max-w-2xl mx-auto">
            <div className="text-6xl mb-4">🎯</div>
            <h2 className="text-2xl font-semibold mb-4">System Initializing...</h2>
            <p className="text-slate-400 mb-6">
              The Mission Control interface is coming online. This premium command center
              will provide real-time mission management, analytics, and platform integration
              for your viral content operations.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="bg-slate-800/50 p-4 rounded-lg">
                <div className="text-green-400 font-semibold">✅ React Frontend</div>
                <div className="text-slate-400">Tesla/SpaceX Design</div>
              </div>
              <div className="bg-slate-800/50 p-4 rounded-lg">
                <div className="text-green-400 font-semibold">✅ Real-time Updates</div>
                <div className="text-slate-400">WebSocket Integration</div>
              </div>
              <div className="bg-slate-800/50 p-4 rounded-lg">
                <div className="text-green-400 font-semibold">✅ API Integration</div>
                <div className="text-slate-400">Mission Coordinator</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
