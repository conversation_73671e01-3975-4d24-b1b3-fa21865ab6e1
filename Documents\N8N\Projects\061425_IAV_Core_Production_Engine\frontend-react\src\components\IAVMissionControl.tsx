import React, { useState, useEffect, useCallback } from 'react';
import { Settings, Plus, Activity, Target, Zap, BarChart3 } from 'lucide-react';
import { Button } from './ui/Button';
import MissionCard from './MissionCard';
import CreateSalvoDialog from './CreateSalvoDialog';
import MissionDossierDialog from './MissionDossierDialog';
import SettingsView from './SettingsView';
import AnalyticsView from './AnalyticsView';
import {
  Mission,
  PlatformConnection,
  CreateMissionRequest,
  mockMissions,
  mockConnections
} from '../types';
import { cn } from '../lib/utils';
import ApiService, { handleApiError } from '../services/api';
import useWebSocket, { WebSocketMessage } from '../hooks/useWebSocket';

const IAVMissionControl: React.FC = () => {
  // State Management
  const [missions, setMissions] = useState<Mission[]>(mockMissions);
  const [connections, setConnections] = useState<PlatformConnection[]>(mockConnections);
  const [selectedMission, setSelectedMission] = useState<Mission | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [currentView, setCurrentView] = useState<'workspace' | 'settings'>('workspace');
  const [loading, setLoading] = useState(false);

  // Computed Values
  const activeMissions = missions.filter(m => m.status === 'active');
  const completedMissions = missions.filter(m => m.status === 'completed');
  const failedMissions = missions.filter(m => m.status === 'failed');
  const connectedPlatforms = connections.filter(c => c.status === 'connected').length;

  // API Functions (TODO: Replace with actual API calls)
  const fetchMissions = async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call
      // const response = await fetch('/api/missions');
      // const data = await response.json();
      // setMissions(data.missions);
      
      // For now, use mock data
      setMissions(mockMissions);
    } catch (error) {
      console.error('Failed to fetch missions:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchConnections = async () => {
    try {
      // TODO: Replace with actual API call
      // const response = await fetch('/api/connections');
      // const data = await response.json();
      // setConnections(data.connections);
      
      // For now, use mock data
      setConnections(mockConnections);
    } catch (error) {
      console.error('Failed to fetch connections:', error);
    }
  };

  const createMission = async (request: CreateMissionRequest) => {
    try {
      // TODO: Replace with actual API call
      // const response = await fetch('/api/missions', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(request)
      // });
      // const data = await response.json();
      // setMissions(prev => [...data.missions, ...prev]);
      
      // For now, create mock mission
      const newMission: Mission = {
        id: `mission_${Date.now()}`,
        title: `${request.title_prefix || 'Operation'} ${Date.now().toString().slice(-4)}`,
        source_url: request.urls[0],
        status: 'queued',
        created_at: new Date().toISOString(),
        progress_percent: 0,
        clips: [],
        total_clips: 0,
        agent_log: [{
          timestamp: new Date().toISOString(),
          agent: 'Mission Coordinator',
          action: 'Mission Created',
          status: 'success',
          message: 'Mission queued for processing'
        }],
        deployments: [],
        retry_count: 0
      };
      
      setMissions(prev => [newMission, ...prev]);
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error('Failed to create mission:', error);
    }
  };

  const authorizePlatform = (platform: string) => {
    // TODO: Implement platform authorization
    const setupUrls = {
      tiktok: 'http://localhost:8602/setup',
      instagram: 'http://localhost:8603/setup',
      youtube: 'http://localhost:8604/setup',
      twitter: 'http://localhost:8605/setup'
    };
    
    const url = setupUrls[platform as keyof typeof setupUrls];
    if (url) {
      window.open(url, '_blank');
    }
  };

  // Effects
  useEffect(() => {
    fetchMissions();
    fetchConnections();
    
    // Set up polling for real-time updates
    const interval = setInterval(() => {
      if (currentView === 'workspace') {
        fetchMissions();
      }
      fetchConnections();
    }, 30000); // Poll every 30 seconds
    
    return () => clearInterval(interval);
  }, [currentView]);

  // Render Functions
  const renderWorkspaceView = () => (
    <div className="space-y-6">
      {/* Command Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            🎯 Mission Control
          </h1>
          <p className="text-muted-foreground mt-1">
            Command center for viral content operations
          </p>
        </div>
        <Button 
          variant="command" 
          size="lg"
          onClick={() => setIsCreateDialogOpen(true)}
          className="flex items-center space-x-2"
        >
          <Plus className="w-5 h-5" />
          <span>Launch Salvo</span>
        </Button>
      </div>

      {/* Mission Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="metric-card">
          <div className="metric-value">{activeMissions.length}</div>
          <div className="metric-label flex items-center">
            <Activity className="w-4 h-4 mr-1" />
            Active Missions
          </div>
        </div>
        <div className="metric-card">
          <div className="metric-value">{completedMissions.length}</div>
          <div className="metric-label flex items-center">
            <Target className="w-4 h-4 mr-1" />
            Completed
          </div>
        </div>
        <div className="metric-card">
          <div className="metric-value">{failedMissions.length}</div>
          <div className="metric-label flex items-center">
            <Zap className="w-4 h-4 mr-1 text-red-400" />
            Failed
          </div>
        </div>
        <div className="metric-card">
          <div className="metric-value">{connectedPlatforms}/4</div>
          <div className="metric-label">Platforms Online</div>
        </div>
      </div>

      {/* Mission Board */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Mission Board</h2>
          <Button 
            variant="outline" 
            size="sm"
            onClick={fetchMissions}
            disabled={loading}
          >
            {loading ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>
        
        {missions.length === 0 ? (
          <div className="glass-panel p-8 text-center">
            <div className="text-4xl mb-4">🎬</div>
            <h3 className="text-lg font-semibold mb-2">No Missions Deployed</h3>
            <p className="text-muted-foreground mb-4">
              Launch your first salvo to begin viral content operations
            </p>
            <Button 
              variant="command"
              onClick={() => setIsCreateDialogOpen(true)}
            >
              Launch First Salvo
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {missions.map((mission) => (
              <MissionCard
                key={mission.id}
                mission={mission}
                onClick={() => setSelectedMission(mission)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Navigation Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="text-2xl font-bold">🚀 IAV</div>
              <nav className="flex space-x-1">
                <Button
                  variant={currentView === 'workspace' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setCurrentView('workspace')}
                >
                  Workspace
                </Button>
                <Button
                  variant={currentView === 'settings' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setCurrentView('settings')}
                >
                  <Settings className="w-4 h-4 mr-1" />
                  Settings
                </Button>
              </nav>
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="text-sm text-muted-foreground">
                {connectedPlatforms}/4 platforms online
              </div>
              <div className={cn(
                'w-2 h-2 rounded-full',
                connectedPlatforms > 0 ? 'bg-green-400' : 'bg-red-400'
              )} />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {currentView === 'workspace' ? renderWorkspaceView() : (
          <SettingsView
            connections={connections}
            onRefresh={fetchConnections}
            onAuthorize={authorizePlatform}
          />
        )}
      </main>

      {/* Dialogs */}
      <CreateSalvoDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onSubmit={createMission}
      />
      
      <MissionDossierDialog
        mission={selectedMission}
        isOpen={!!selectedMission}
        onClose={() => setSelectedMission(null)}
      />
    </div>
  );
};

export default IAVMissionControl;
